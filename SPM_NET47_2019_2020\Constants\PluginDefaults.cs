﻿namespace SPM_NET47_2019_2020.Defaults
{
    public static class PluginDefaults
    {
        public const string DefaultPointLayer = "spm_Point";
        public const int DefaultPointLayerColor = 2;
        public const string DefaultPointNumberLayer = "spm_PointNumber";
        public const int DefaultPointNumberLayerColor = 1;
        public const string DefaultTableLayer = "spm_table";
        public const int DefaultTableLayerColor = 3;
        public const int DefaultPointDisplayImage = 34;
        public const double DefaultPointSize = 0.25;
        public const string DefaultPointSizeMode = "Set Size Relative to Screen";
        public const double DefaultPointNumberTextHeight = 0.18;
        public const string DefaultTableStyle = "spm_TableStyle";
        public const int DefaultMaxRowPerTable = 15;
        public const bool DefaultShowTableTitle = true;
        public const string DefaultTableTitle = "Survey Points";
        public const bool DefaultShowPointNumber = true;
        public const bool DefaultShowElevation = true;
        public const bool DefaultShowDescription = true;
        public const bool DefaultRecordHistory = true;
        public const int DefaultRecordHistoryLimit = 5000;
        public const string DefaultTheme = "System";
    }
}
