﻿using System;
using System.Text.RegularExpressions;

namespace SPM_NET47_2019_2020.Utils
{

    public static class TableStyleNameValidator
    {
        /// <summary>
        /// Validates and cleans a table style name according to AutoCAD's table style naming rules.
        /// Throws an exception if the name is reserved (e.g., "Standard").
        /// </summary>
        /// <param name="input">The input table style name.</param>
        /// <returns>A valid table style name that adheres to the naming rules.</returns>
        public static string Validate(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            try
            {
                // Replace whitespace with underscores
                string result = Regex.Replace(input, @"\s+", "_");

                // Remove any disallowed characters (allowed: letters, digits, hyphen, underscore, dollar sign)
                result = Regex.Replace(result, @"[^A-Za-z0-9\-_\$]", "");

                // Ensure the name does not exceed 255 characters
                if (result.Length > 255)
                    result = result.Substring(0, 255);

                // Check for reserved table style name "Standard"
                if (string.Equals(result, "Standard", StringComparison.OrdinalIgnoreCase))
                {
                    throw new ArgumentException("The table style name 'Standard' is reserved. Please choose a different name.");
                }

                return result;
            }
            catch (Exception ex)
            {
                // Optionally log the exception here
                // For now, we simply rethrow so the caller can handle it appropriately
                throw new InvalidOperationException("Error validating table style name.", ex);
            }
        }
    }

}
