﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="SPM_NET47_2019_2020.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <userSettings>
        <SPM_NET47_2019_2020.Properties.Settings>
            <setting name="PointLayer" serializeAs="String">
                <value>spm_Point</value>
            </setting>
            <setting name="PointLayerColor" serializeAs="String">
                <value>2</value>
            </setting>
            <setting name="PointNumberLayer" serializeAs="String">
                <value>spm_PointNumber</value>
            </setting>
            <setting name="PointNumberLayerColor" serializeAs="String">
                <value>1</value>
            </setting>
            <setting name="TableLayer" serializeAs="String">
                <value>spm_table</value>
            </setting>
            <setting name="TableLayerColor" serializeAs="String">
                <value>3</value>
            </setting>
            <setting name="PointDisplayImage" serializeAs="String">
                <value>35</value>
            </setting>
            <setting name="PointSize" serializeAs="String">
                <value>0.25</value>
            </setting>
            <setting name="PointSizeMode" serializeAs="String">
                <value>Set Size Relative to Screen</value>
            </setting>
            <setting name="PointNumberTextHeight" serializeAs="String">
                <value>0.18</value>
            </setting>
            <setting name="TableStyleName" serializeAs="String">
                <value>spm_TableStyle</value>
            </setting>
            <setting name="MaxRowPerTable" serializeAs="String">
                <value>15</value>
            </setting>
            <setting name="ShowTableTitle" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="TableTitle" serializeAs="String">
                <value>Survey Points</value>
            </setting>
            <setting name="ShowPointNumber" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="ShowElevation" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="ShowDescription" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="RecordHistory" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="RecordHistoryLimit" serializeAs="String">
                <value>5000</value>
            </setting>
            <setting name="Theme" serializeAs="String">
                <value>System</value>
            </setting>
        </SPM_NET47_2019_2020.Properties.Settings>
    </userSettings>
</configuration>