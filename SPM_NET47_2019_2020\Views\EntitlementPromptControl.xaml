﻿<UserControl
    x:Class="SPM_NET47_2019_2020.Views.EntitlementPromptControl"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:SPM_NET47_2019_2020.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Width="400"
    Height="600"
    MinWidth="400"
    MinHeight="600"
    FontFamily="Segoe UI"
    Loaded="EntitlementPromptControl_Loaded"
    mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/SPM_NET47_2019_2020;component/ResourceDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!--  Color Palette  -->
            <SolidColorBrush x:Key="BackgroundColor" Color="#F9FAFB" />
            <!--  Tailwind gray-50  -->
            <SolidColorBrush x:Key="CardBackgroundColor" Color="#ebebeb" />
            <!--  White  -->
            <SolidColorBrush x:Key="BorderColor" Color="#E5E7EB" />
            <!--  Tailwind gray-200  -->
            <SolidColorBrush x:Key="PrimaryTextColor" Color="#1F2937" />
            <!--  Tailwind gray-800  -->
            <SolidColorBrush x:Key="SecondaryTextColor" Color="#4B5563" />
            <!--  Tailwind gray-600  -->
            <SolidColorBrush x:Key="MutedTextColor" Color="#6B7280" />
            <!--  Tailwind gray-500  -->
            <SolidColorBrush x:Key="AccentColorBrush" Color="#3B82F6" />
            <!--  Tailwind blue-500  -->
            <SolidColorBrush x:Key="AccentHoverColorBrush" Color="#2563EB" />
            <!--  Tailwind blue-600  -->
            <SolidColorBrush x:Key="AccentPressedColorBrush" Color="#1D4ED8" />
            <!--  Tailwind blue-700  -->
            <SolidColorBrush x:Key="ProgressBarTrackColor" Color="#E5E7EB" />
            <!--  Tailwind gray-200  -->


            <!--  Converters for Visibility  -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
            <!--  This converter is CRITICAL if you use the AdvancedProgressBarStyle. Ensure the class exists and namespace is correct.  -->
            <local:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />


            <!--  Typography  -->
            <Style x:Key="BaseTextBlockStyle" TargetType="TextBlock">
                <Setter Property="Foreground" Value="{StaticResource PrimaryTextColor}" />
                <Setter Property="TextWrapping" Value="Wrap" />
            </Style>

            <Style
                x:Key="TitleTextStyle"
                BasedOn="{StaticResource BaseTextBlockStyle}"
                TargetType="TextBlock">
                <Setter Property="FontSize" Value="20" />
                <Setter Property="FontWeight" Value="SemiBold" />
                <Setter Property="TextAlignment" Value="Center" />
            </Style>

            <Style
                x:Key="BodyTextStyle"
                BasedOn="{StaticResource BaseTextBlockStyle}"
                TargetType="TextBlock">
                <Setter Property="FontSize" Value="14" />
                <Setter Property="Foreground" Value="{StaticResource SecondaryTextColor}" />
                <Setter Property="TextAlignment" Value="Center" />
                <Setter Property="LineHeight" Value="20" />
            </Style>

            <Style
                x:Key="StatusTextStyle"
                BasedOn="{StaticResource BaseTextBlockStyle}"
                TargetType="TextBlock">
                <Setter Property="FontSize" Value="16" />
                <Setter Property="FontWeight" Value="SemiBold" />
                <Setter Property="Foreground" Value="{StaticResource PrimaryTextColor}" />
                <Setter Property="TextAlignment" Value="Center" />
            </Style>

            <Style x:Key="LinkTextStyle" TargetType="Hyperlink">
                <Setter Property="Foreground" Value="{StaticResource AccentColorBrush}" />
                <Setter Property="TextDecorations" Value="None" />
                <Style.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter Property="Foreground" Value="{StaticResource AccentHoverColorBrush}" />
                        <Setter Property="TextDecorations" Value="Underline" />
                    </Trigger>
                </Style.Triggers>
            </Style>

            <!--  Button Style  -->
            <Style x:Key="ModernButtonStyle" TargetType="Button">
                <Setter Property="Background" Value="{StaticResource AccentColorBrush}" />
                <Setter Property="Foreground" Value="White" />
                <Setter Property="BorderThickness" Value="0" />
                <Setter Property="Padding" Value="12,8" />
                <Setter Property="FontSize" Value="14" />
                <Setter Property="FontWeight" Value="SemiBold" />
                <Setter Property="Cursor" Value="Hand" />
                <Setter Property="HorizontalAlignment" Value="Center" />
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="Button">
                            <Border
                                x:Name="border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="6"
                                SnapsToDevicePixels="True">
                                <ContentPresenter
                                    x:Name="contentPresenter"
                                    Margin="{TemplateBinding Padding}"
                                    HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                    VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                    Focusable="False"
                                    RecognizesAccessKey="True"
                                    SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="border" Property="Background" Value="{StaticResource AccentHoverColorBrush}" />
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter TargetName="border" Property="Background" Value="{StaticResource AccentPressedColorBrush}" />
                                    <Setter TargetName="border" Property="RenderTransform">
                                        <Setter.Value>
                                            <ScaleTransform ScaleX="0.98" ScaleY="0.98" />
                                        </Setter.Value>
                                    </Setter>
                                    <Setter TargetName="border" Property="RenderTransformOrigin" Value="0.5,0.5" />
                                </Trigger>
                                <Trigger Property="IsEnabled" Value="False">
                                    <Setter TargetName="border" Property="Background" Value="{StaticResource BorderColor}" />
                                    <Setter Property="Foreground" Value="{StaticResource MutedTextColor}" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

            <!--  Advanced ProgressBar Style  -->
            <!--  This styles the default indeterminate animation. For a fully custom animation, you'd need more complex templating.  -->
            <!--  Advanced ProgressBar Style  -->
            <Style x:Key="AdvancedProgressBarStyle" TargetType="ProgressBar">
                <Setter Property="Height" Value="8" />
                <Setter Property="BorderThickness" Value="0" />
                <Setter Property="Background" Value="{StaticResource ProgressBarTrackColor}" />
                <!--  Track color  -->
                <Setter Property="Foreground" Value="{StaticResource AccentColorBrush}" />
                <!--  Progress color  -->
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="ProgressBar">
                            <Grid x:Name="TemplateRoot">
                                <!--  Background track of the ProgressBar  -->
                                <Border
                                    x:Name="PART_Track"
                                    Background="{TemplateBinding Background}"
                                    CornerRadius="4" />

                                <!--  Determinate part of the ProgressBar, hidden when IsIndeterminate is true  -->
                                <Border
                                    x:Name="PART_Indicator"
                                    HorizontalAlignment="Left"
                                    Background="{TemplateBinding Foreground}"
                                    CornerRadius="4"
                                    Visibility="{TemplateBinding IsIndeterminate,
                                                                 Converter={StaticResource InverseBooleanToVisibilityConverter}}" />

                                <!--  Custom Indeterminate Animation Element Root  -->
                                <Border
                                    x:Name="IndeterminateRoot"
                                    Background="Transparent"
                                    ClipToBounds="True"
                                    CornerRadius="4"
                                    Visibility="{TemplateBinding IsIndeterminate,
                                                                 Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <Grid>
                                        <!--  The animated shimmer/gradient element  -->
                                        <Rectangle
                                            x:Name="IndeterminateGradientFill"
                                            Width="100"
                                            Height="{TemplateBinding Height}"
                                            HorizontalAlignment="Left"
                                            Fill="{TemplateBinding Foreground}">
                                            <Rectangle.RenderTransform>
                                                <TranslateTransform X="-100" />
                                            </Rectangle.RenderTransform>
                                            <Rectangle.RenderTransformOrigin>0.5,0.5</Rectangle.RenderTransformOrigin>
                                        </Rectangle>

                                        <!--
                                            This 'Animation' element is targeted by the storyboard.
                                            It's an empty, transparent border used as an animation target.
                                        -->
                                        <Border
                                            x:Name="Animation"
                                            IsHitTestVisible="False"
                                            Opacity="0">
                                            <Border.RenderTransform>
                                                <TransformGroup>
                                                    <ScaleTransform />
                                                </TransformGroup>
                                            </Border.RenderTransform>
                                        </Border>
                                    </Grid>
                                </Border>
                                <VisualStateManager.VisualStateGroups>
                                    <VisualStateGroup x:Name="CommonStates">
                                        <VisualState x:Name="Determinate" />
                                        <VisualState x:Name="Indeterminate">
                                            <Storyboard RepeatBehavior="Forever">
                                                <!--  Animation for 'Animation' element (often for opacity or scale effects)  -->
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Animation" Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="1" />
                                                    <EasingDoubleKeyFrame KeyTime="0:0:1" Value="1" />
                                                    <EasingDoubleKeyFrame KeyTime="0:0:2" Value="1" />
                                                </DoubleAnimationUsingKeyFrames>
                                                <!--  Animation for the sliding gradient fill  -->
                                                <DoubleAnimationUsingKeyFrames Storyboard.TargetName="IndeterminateGradientFill" Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)">
                                                    <EasingDoubleKeyFrame KeyTime="0" Value="-100" />
                                                    <!--  Start off-screen left, width of IndeterminateGradientFill  -->
                                                    <EasingDoubleKeyFrame KeyTime="0:0:1.5" Value="400">
                                                        <!--  Slide across (adjust 400 based on UserControl width + element width)  -->
                                                        <EasingDoubleKeyFrame.EasingFunction>
                                                            <QuadraticEase EasingMode="EaseInOut" />
                                                        </EasingDoubleKeyFrame.EasingFunction>
                                                    </EasingDoubleKeyFrame>
                                                </DoubleAnimationUsingKeyFrames>
                                            </Storyboard>
                                        </VisualState>
                                    </VisualStateGroup>
                                </VisualStateManager.VisualStateGroups>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>

        </ResourceDictionary>
    </UserControl.Resources>


    <Grid Background="{StaticResource BackgroundColor}">

        <Border
            Margin="15"
            Padding="25"
            Background="{StaticResource CardBackgroundColor}"
            BorderBrush="{StaticResource BorderColor}"
            BorderThickness="1"
            CornerRadius="8">
            <Border.Effect>
                <DropShadowEffect
                    BlurRadius="10"
                    Direction="270"
                    Opacity="0.05"
                    ShadowDepth="1"
                    Color="#000000" />
            </Border.Effect>

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <!--  Logo  -->
                    <RowDefinition Height="Auto" />
                    <!--  Status  -->
                    <RowDefinition Height="Auto" />
                    <!--  Progress Bar  -->
                    <RowDefinition Height="*" />
                    <!--  Instructions  -->
                    <RowDefinition Height="Auto" />
                    <!--  Action Buttons  -->
                    <RowDefinition Height="Auto" />
                    <!--  Help Link  -->
                </Grid.RowDefinitions>

                <!--  Logo Area  -->
                <ContentControl
                    Grid.Row="0"
                    Width="260"
                    Height="130"
                    Margin="0,0,0,30"
                    HorizontalAlignment="Center"
                    Template="{StaticResource HomeLogoTemplate}" />

                <!--  Status Area  -->
                <TextBlock
                    Name="txtStatus"
                    Grid.Row="1"
                    Margin="0,0,0,10"
                    Style="{StaticResource StatusTextStyle}"
                    Text="Initializing..." />

                <!--  Option 1: Simpler ProgressBar with styled container (currently active in code-behind)  -->
                <!--  The track for this ProgressBar is handled by the surrounding Border  -->
                <!--<Border
                    Name="progressBarContainer"
                    Grid.Row="2"
                    Height="8"
                    Margin="0,5,0,20"
                    Background="{StaticResource ProgressBarTrackColor}"
                    CornerRadius="4"
                    Visibility="Collapsed">
                    <ProgressBar
                        Name="progressBarSimple"
                        Height="8"
                        Background="Transparent"
                        BorderThickness="0"
                        Foreground="{StaticResource AccentColorBrush}"
                        IsIndeterminate="True" />
                </Border>-->

                <!--  Option 2: Advanced Templated ProgressBar (Uncomment to use, and update code-behind to target 'progressBarAdvanced')  -->

                <ProgressBar
                    Name="progressBarAdvanced"
                    Grid.Row="2"
                    Margin="0,5,0,20"
                    IsIndeterminate="True"
                    Style="{StaticResource AdvancedProgressBarStyle}"
                    Visibility="Collapsed" />

                <!--  Instructions/Messages Area  -->
                <ScrollViewer
                    Grid.Row="3"
                    Margin="0,0,0,25"
                    VerticalScrollBarVisibility="Auto">
                    <TextBlock
                        Name="txtInstructions"
                        Style="{StaticResource BodyTextStyle}"
                        Text="Please wait while we verify your entitlement. This might take a few moments. If this persists, please check your internet connection."
                        Visibility="Collapsed" />
                </ScrollViewer>

                <!--  Action Buttons Area  -->
                <StackPanel
                    Grid.Row="4"
                    Margin="0,0,0,20"
                    HorizontalAlignment="Stretch"
                    Orientation="Vertical">
                    <Button
                        Name="btnLogin"
                        Margin="0,0,0,10"
                        Click="BtnLogin_Click"
                        Content="Sign In (Autodesk)"
                        Style="{StaticResource ModernButtonStyle}"
                        Visibility="Collapsed" />
                    <Button
                        Name="btnRetry"
                        Click="BtnRetry_Click"
                        Content="Retry Verification"
                        Style="{StaticResource ModernButtonStyle}"
                        Visibility="Collapsed" />
                </StackPanel>

                <!--  Help/Store Link  -->
                <TextBlock
                    Name="linkHelp"
                    Grid.Row="5"
                    Margin="0,10,0,0"
                    HorizontalAlignment="Center"
                    Visibility="Visible">
                    <Hyperlink
                        NavigateUri="https://www.autodesk.com/support"
                        RequestNavigate="Hyperlink_RequestNavigate"
                        Style="{StaticResource LinkTextStyle}">
                        Need a license or help? Click here.
                    </Hyperlink>
                </TextBlock>
            </Grid>
        </Border>
    </Grid>

</UserControl>
