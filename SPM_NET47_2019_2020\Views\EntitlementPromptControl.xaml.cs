﻿using SPM_NET47_2019_2020.Managers;
using System;
using System.Diagnostics;
using System.Globalization;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Navigation;

namespace SPM_NET47_2019_2020.Views
{

    public partial class EntitlementPromptControl : UserControl
    {
        private readonly EntitlementManager _entitlementManager;
        public event Action EntitlementSucceeded;
        private bool _isChecking = false; // Prevent overlapping checks

        // P/Invoke for Login - defined here for direct use
        [DllImport("AcConnectWebServices.arx", CharSet = CharSet.Unicode, EntryPoint = "AcConnectWebServicesLogin")]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool AcConnectWebServicesLogin();


        // Constructor now takes the manager instance
        public EntitlementPromptControl(EntitlementManager manager)
        {
            InitializeComponent();
            _entitlementManager = manager ?? throw new ArgumentNullException(nameof(manager));
        }

        private async void EntitlementPromptControl_Loaded(object sender, RoutedEventArgs e)
        {
            // Automatically start check when the control is loaded into the visual tree.
            {
                await PerformCheckAsync();
            }
        }

        private async Task PerformCheckAsync()
        {
            if (_isChecking)
            {
                return; // Don't start a new check if one is running
            }

            _isChecking = true;

            UpdateUIForStatus(null, isChecking: true);

            EntitlementStatus currentStatus = EntitlementStatus.Unknown;
            try
            {
                currentStatus = await _entitlementManager.CheckUserEntitlementAsync();
            }
            catch (Exception ex)
            {
                // Log unexpected errors from the check process itself
                // Consider a more robust logging mechanism if available
                System.Diagnostics.Debug.WriteLine($"PointFlowCAD UI Error during check: {ex.Message}");
                if (Autodesk.AutoCAD.ApplicationServices.Application.DocumentManager?.MdiActiveDocument?.Editor != null)
                {
                    Autodesk.AutoCAD.ApplicationServices.Application.DocumentManager.MdiActiveDocument.Editor.WriteMessage($"\nPointFlowCAD UI Error during check: {ex.Message}");
                }
                currentStatus = EntitlementStatus.CheckError; // Mark as error
            }
            finally
            {
                UpdateUIForStatus(currentStatus, isChecking: false);
                _isChecking = false; // Allow new checks now
            }

            if (currentStatus == EntitlementStatus.Entitled || currentStatus == EntitlementStatus.OfflineCacheValid)
            {
                await Task.Delay(1500); // Small delay for user to see success message
                EntitlementSucceeded?.Invoke();
            }
        }

        private void UpdateUIForStatus(EntitlementStatus? status, bool isChecking)
        {
            // Ensure UI updates are on the UI thread, though await usually handles this.
            // If issues, wrap with: this.Dispatcher.Invoke(() => { ... });

            if (isChecking)
            {
                txtStatus.Text = "Verifying license, please wait...";

                // Using the simpler ProgressBar setup (progressBarContainer + progressBarSimple)
                //progressBarContainer.Visibility = Visibility.Visible;
                //progressBarSimple.IsIndeterminate = true;

                progressBarAdvanced.Visibility = Visibility.Visible; // MODIFIED
                progressBarAdvanced.IsIndeterminate = true;          // MODIFIED

                txtInstructions.Visibility = Visibility.Collapsed;
                btnLogin.Visibility = Visibility.Collapsed;
                btnRetry.Visibility = Visibility.Collapsed;
                linkHelp.Visibility = Visibility.Visible;
                return;
            }

            // Stop loading indicator
            //progressBarContainer.Visibility = Visibility.Collapsed;
            //progressBarSimple.IsIndeterminate = false;

            progressBarAdvanced.Visibility = Visibility.Collapsed; // MODIFIED
            progressBarAdvanced.IsIndeterminate = false;          // MODIFIED

            btnLogin.Visibility = Visibility.Collapsed;
            btnRetry.Visibility = Visibility.Collapsed;

            switch (status)
            {
                case EntitlementStatus.Entitled:
                case EntitlementStatus.OfflineCacheValid:
                    txtStatus.Text = "License verified successfully!";
                    txtInstructions.Text = "Starting PointFlowCAD...";
                    txtInstructions.Visibility = Visibility.Visible;
                    linkHelp.Visibility = Visibility.Collapsed;
                    break;

                case EntitlementStatus.LoginRequired:
                    txtStatus.Text = "Sign-In Required";
                    txtInstructions.Text = "Please sign in to your Autodesk Account to verify your PointFlowCAD license.";
                    txtInstructions.Visibility = Visibility.Visible;
                    btnLogin.Visibility = Visibility.Visible;
                    break;

                case EntitlementStatus.NotEntitled:
                    txtStatus.Text = "License Not Found";
                    txtInstructions.Text = "No valid PointFlowCAD license was found for your Autodesk Account. Please purchase a license or contact support.";
                    txtInstructions.Visibility = Visibility.Visible;
                    linkHelp.Visibility = Visibility.Visible;
                    break;

                case EntitlementStatus.OfflineCacheExpired:
                    txtStatus.Text = "Offline Access Expired";
                    txtInstructions.Text = "Your offline access period has expired. Please connect to the internet and retry verification.";
                    txtInstructions.Visibility = Visibility.Visible;
                    btnRetry.Visibility = Visibility.Visible;
                    break;

                case EntitlementStatus.ConnectivityError:
                    txtStatus.Text = "Connection Error";
                    txtInstructions.Text = "Could not connect to the Autodesk licensing server. Please check your internet connection and retry.";
                    txtInstructions.Visibility = Visibility.Visible;
                    btnRetry.Visibility = Visibility.Visible;
                    break;

                case EntitlementStatus.ServiceError:
                    txtStatus.Text = "Server Error";
                    txtInstructions.Text = "There was a problem communicating with the Autodesk licensing server. Please retry verification later.";
                    txtInstructions.Visibility = Visibility.Visible;
                    btnRetry.Visibility = Visibility.Visible;
                    break;

                case EntitlementStatus.CheckError:
                case EntitlementStatus.Unknown:
                default:
                    txtStatus.Text = "Verification Error";
                    txtInstructions.Text = "An unexpected error occurred during license verification. Please retry or contact support.";
                    txtInstructions.Visibility = Visibility.Visible;
                    btnRetry.Visibility = Visibility.Visible;
                    break;
            }
        }

        private async void BtnLogin_Click(object sender, RoutedEventArgs e)
        {
            if (_isChecking)
            {
                return;
            }

            txtStatus.Text = "Please complete the Autodesk Sign-In process...";
            txtInstructions.Visibility = Visibility.Collapsed;
            btnLogin.Visibility = Visibility.Collapsed;
            //progressBarContainer.Visibility = Visibility.Collapsed;
            progressBarAdvanced.Visibility = Visibility.Collapsed; // MODIFIED


            bool loginComplete = false;
            try
            {
                // This call might block the UI thread if not handled carefully.
                // If AcConnectWebServicesLogin is synchronous and long-running,
                // consider Task.Run, but be mindful of COM threading if it interacts with AutoCAD.
                loginComplete = AcConnectWebServicesLogin();
            }
            catch (DllNotFoundException)
            {
                txtStatus.Text = "Error: Autodesk login components missing.";
                ShowAutoCADAlert("Could not initiate Autodesk Sign-In. Required components (AcConnectWebServices.arx) might be missing or not loaded.");
                UpdateUIForStatus(EntitlementStatus.LoginRequired, isChecking: false);
                return;
            }
            catch (Exception ex)
            {
                txtStatus.Text = $"Error during login: {ex.Message}";
                ShowAutoCADAlert($"An error occurred trying to initiate Autodesk Sign-In: {ex.Message}");
                UpdateUIForStatus(EntitlementStatus.LoginRequired, isChecking: false);
                return;
            }

            if (loginComplete)
            {
                txtStatus.Text = "Sign-in process completed. Re-checking license...";
                await Task.Delay(1500); // Give AutoCAD a moment
                await PerformCheckAsync();
            }
            else
            {
                txtStatus.Text = "Sign-in cancelled or failed.";
                UpdateUIForStatus(EntitlementStatus.LoginRequired, isChecking: false);
            }
        }

        private async void BtnRetry_Click(object sender, RoutedEventArgs e)
        {
            if (!_isChecking)
            {
                await PerformCheckAsync();
            }
        }

        private void Hyperlink_RequestNavigate(object sender, RequestNavigateEventArgs e)
        {
            try
            {
                Process.Start(new ProcessStartInfo(e.Uri.AbsoluteUri) { UseShellExecute = true });
                e.Handled = true;
            }
            catch (Exception ex)
            {
                ShowAutoCADAlert($"Could not open link: {ex.Message}");
            }
        }

        // Helper to show alert dialog in AutoCAD context
        private void ShowAutoCADAlert(string message)
        {
            try
            {
                // Check if AutoCAD application services are available
                if (Autodesk.AutoCAD.ApplicationServices.Core.Application.DocumentManager != null)
                {
                    Autodesk.AutoCAD.ApplicationServices.Core.Application.ShowAlertDialog(message);
                }
                else
                {
                    // Fallback if AutoCAD context is not fully available (e.g., during early init or testing)
                    MessageBox.Show(message, "PointFlowCAD Info", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                // Fallback to a standard MessageBox if AutoCAD specific UI fails
                System.Diagnostics.Debug.WriteLine($"Failed to show AutoCAD alert: {ex.Message}");
                MessageBox.Show(message, "PointFlowCAD Info", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }

    // --- Converters ---
    // If you place these in a separate Converters.cs file and a different namespace (e.g., PointFlowCAD.Converters),
    // remember to update the xmlns:local in your XAML accordingly.
    // For simplicity, placing it here if it's only used by this UserControl.

    public class InverseBooleanToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                // If boolValue (e.g., IsIndeterminate) is true, we want Collapsed for the element this converter is applied to.
                // If boolValue is false, we want Visible.
                return boolValue ? Visibility.Collapsed : Visibility.Visible;
            }
            return Visibility.Collapsed; // Default or if value is not bool
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // Not typically needed for one-way binding to Visibility
            throw new NotImplementedException();
        }
    }

    // Note: The BooleanToVisibilityConverter is built into WPF, so you typically don't need to define it
    // unless you need a custom version or are targeting a platform without it.
    // The XAML <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
    // will use the system's default one.
}
