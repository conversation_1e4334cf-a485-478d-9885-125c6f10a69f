﻿<UserControl
    x:Class="SPM_NET47_2019_2020.Views.ExportPalette"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:SPM_NET47_2019_2020.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:partialViews="clr-namespace:SPM_NET47_2019_2020.PartialViews"
    xmlns:services="clr-namespace:SPM_NET47_2019_2020.Services"
    Width="400"
    Height="600"
    MinWidth="400"
    MinHeight="600"
    mc:Ignorable="d">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="/SPM_NET47_2019_2020;component/ResourceDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid Background="#FFECECEC">
        <!--  Main Grid Definition  -->
        <Grid.RowDefinitions>
            <!--  Header  -->
            <RowDefinition Height="Auto" />
            <!--  Picking  -->
            <RowDefinition Height="Auto" />
            <!--  Controls (Search, Delete All, Undo, and Redo)  -->
            <RowDefinition Height="Auto" />
            <!--  DataGrid (Point List)  -->
            <RowDefinition Height="Auto" />
            <!--  Footer (point counter, Draw Table, Formats Informations (Like SDR), and Export)  -->
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>


        <!--#region Header-->
        <partialViews:Header
            Grid.Row="0"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Top"
            BreadcrumbText="EXPORT" />
        <!--#endregion-->


        <!--#region Picking-->
        <Grid Grid.Row="1" Margin="0,3,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="Auto" />
            </Grid.ColumnDefinitions>

            <!--#region Smart (Objects like Blocks and Text) Picking-->
            <GroupBox
                Grid.Column="0"
                Width="168"
                Margin="5,0,1.5,0"
                Background="#80FFFFFF"
                BorderBrush="#80BFB6EA"
                BorderThickness="2"
                FontFamily="Poppins"
                FontWeight="DemiBold"
                Foreground="#FF55657F"
                Header="Objects">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <!--  Smart Pick Options Button  -->
                    <Button
                        x:Name="SmartPickOptionsButton"
                        Grid.Column="0"
                        Width="75"
                        Height="27"
                        Background="#FFD9D9D9"
                        BorderBrush="{x:Null}"
                        BorderThickness="1"
                        Click="SmartPickOptionsButton_Click"
                        Cursor="Hand"
                        Foreground="#FF55657F"
                        IsEnabled="True"
                        ToolTip="Draw listed points on the model"
                        Visibility="Collapsed">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border
                                    x:Name="Border"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="5">
                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter TargetName="Border" Property="Background" Value="#FFB5B5B5" />
                                        <Setter TargetName="Border" Property="BorderBrush" Value="#FFB5B5B5" />
                                    </Trigger>
                                    <Trigger Property="IsPressed" Value="True">
                                        <Setter TargetName="Border" Property="Background" Value="#FF9B9B9B" />
                                        <Setter TargetName="Border" Property="BorderBrush" Value="#FF9B9B9B" />
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Button.Template>

                        <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                            <Viewbox
                                xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                                xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                                Width="17"
                                Height="17"
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center"
                                Stretch="Uniform">
                                <Canvas Width="16" Height="16">

                                    <Path Fill="#FF687A99">
                                        <Path.Data>
                                            <PathGeometry Figures="M0 1.5A1.5 1.5 0 0 1 1.5 0h2A1.5 1.5 0 0 1 5 1.5v2A1.5 1.5 0 0 1 3.5 5h-2A1.5 1.5 0 0 1 0 3.5zM1.5 1a.5.5 0 0 0-.5.5v2a.5.5 0 0 0 .5.5h2a.5.5 0 0 0 .5-.5v-2a.5.5 0 0 0-.5-.5zM0 8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm1 3v2a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2zm14-1V8a1 1 0 0 0-1-1H2a1 1 0 0 0-1 1v2zM2 8.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5m0 4a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5" />
                                        </Path.Data>
                                    </Path>
                                </Canvas>
                            </Viewbox>
                            <TextBlock
                                Margin="5,0,0,0"
                                VerticalAlignment="Center"
                                FontFamily="{StaticResource PoppinsFont}"
                                FontSize="10"
                                Text="Options"
                                ToolTip="Inserts points from the DataGrid as geometric entities in the model space." />
                        </StackPanel>
                    </Button>

                    <!--  Smart Pick Instructions  -->
                    <TextBlock
                        x:Name="SmartPickGuidance"
                        Grid.Column="0"
                        Width="93"
                        Margin="2"
                        HorizontalAlignment="Center"
                        FontSize="10.25"
                        FontWeight="SemiBold"
                        Foreground="#3B755F"
                        Text="Select blocks or text to extract points, then configure options."
                        TextAlignment="Left"
                        TextWrapping="Wrap"
                        Visibility="Visible" />


                    <!--  Smart Pick Button  -->
                    <Button
                        x:Name="SmartPickButton"
                        Grid.Column="1"
                        Width="50"
                        Height="50"
                        Margin="3,0,3,0"
                        VerticalAlignment="Center"
                        Background="#FFD9D9D9"
                        BorderBrush="#FF55657F"
                        BorderThickness="0"
                        Click="SmartPickButton_Click"
                        Cursor="Hand"
                        FontFamily="{StaticResource PoppinsFont}"
                        Foreground="#FF55657F"
                        ToolTip="Identify and extract points from blocks or text in model space. You can choose to delete the original objects after conversion.">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border
                                    x:Name="border"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="10">
                                    <StackPanel
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Orientation="Vertical">

                                        <!--  Icon  -->

                                        <Viewbox
                                            Width="25"
                                            Height="25"
                                            Margin="0,2,0,0"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Stretch="Uniform">
                                            <Canvas Width="30.36" Height="36.56">
                                                <Path Fill="{TemplateBinding Foreground}">
                                                    <Path.Data>
                                                        <PathGeometry Figures="M14.28 25.12h12.44c2.43 0 3.64-1.23 3.64-3.66V10.82a3.62 3.62 0 0 0-1.11-3.13l-6.46-6.58A3.49 3.49 0 0 0 19.84 0h-5.56c-2.42 0-3.64 1.24-3.64 3.68v17.78c0 2.45 1.22 3.66 3.64 3.66m.09-1.88a1.64 1.64 0 0 1-1.84-1.82V3.72a1.66 1.66 0 0 1 1.85-1.83h5.21V8.7a2 2 0 0 0 2.2 2.2h6.68v10.52a1.64 1.64 0 0 1-1.84 1.82ZM22 9.13c-.46 0-.65-.19-.65-.66V2.25l6.75 6.88Z" />
                                                    </Path.Data>
                                                </Path>
                                                <Path
                                                    Stroke="{TemplateBinding Foreground}"
                                                    StrokeEndLineCap="Round"
                                                    StrokeLineJoin="Round"
                                                    StrokeStartLineCap="Round">
                                                    <Path.Data>
                                                        <PathGeometry Figures="M26 25.06a5.2 5.2 0 0 1 1 3.5 7.5 7.5 0 1 1-15 0 5.57 5.57 0 0 1 2-4.5" />
                                                    </Path.Data>
                                                </Path>
                                                <Path Fill="{TemplateBinding Foreground}">
                                                    <Path.Data>
                                                        <PathGeometry Figures="M20.94 28.83a1.8 1.8 0 0 1-.49.85m0 0a1.8 1.8 0 0 1-1.33.55H1.89a1.8 1.8 0 0 1-1.34-.55A1.8 1.8 0 0 1 0 28.35V15.78a1.8 1.8 0 0 1 .55-1.33 1.8 1.8 0 0 1 1.34-.55h17.23A1.88 1.88 0 0 1 21 15.78v12.57a2 2 0 0 1-.06.48m-19 .23h17.18a.66.66 0 0 0 .51-.2.67.67 0 0 0 .2-.51V15.78a.69.69 0 0 0-.71-.72H1.88a.69.69 0 0 0-.71.72v12.57a.67.67 0 0 0 .2.51.68.68 0 0 0 .52.2m-.72 0" />
                                                    </Path.Data>
                                                </Path>
                                            </Canvas>
                                        </Viewbox>

                                        <!--  Text  -->

                                        <TextBlock
                                            Margin="0,2,0,0"
                                            HorizontalAlignment="Center"
                                            FontSize="11"
                                            FontWeight="SemiBold"
                                            Foreground="{TemplateBinding Foreground}"
                                            Text="SMART" />
                                    </StackPanel>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter TargetName="border" Property="Background" Value="#FFB5B5B5" />
                                        <Setter TargetName="border" Property="BorderBrush" Value="#FFB5B5B5" />
                                    </Trigger>
                                    <Trigger Property="IsPressed" Value="True">
                                        <Setter TargetName="border" Property="Background" Value="#FF9B9B9B" />
                                        <Setter TargetName="border" Property="BorderBrush" Value="#FF9B9B9B" />
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>

                    <!--  Smart Pick Popup  -->
                    <Popup
                        x:Name="SmartPickOptionsPopup"
                        AllowsTransparency="True"
                        IsOpen="False"
                        Placement="Bottom"
                        PlacementTarget="{Binding ElementName=SmartPickOptionsButton}"
                        PopupAnimation="Fade"
                        StaysOpen="False">


                        <Border
                            Width="300"
                            Height="Auto"
                            Padding="10"
                            Background="#FFFFFF"
                            BorderBrush="#80BFB6EA"
                            BorderThickness="2"
                            CornerRadius="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>

                                <!--  Header with Title and Close Button  -->
                                <Grid Grid.Row="0" Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <TextBlock
                                        Grid.Column="0"
                                        VerticalAlignment="Center"
                                        FontFamily="{StaticResource PoppinsFont}"
                                        FontSize="13"
                                        FontWeight="SemiBold"
                                        Foreground="#FF55657F"
                                        Text="Smart Pick Options" />
                                    <Button
                                        x:Name="SmartPickPopupCloseButton"
                                        Grid.Column="1"
                                        Width="30"
                                        Height="30"
                                        Background="Transparent"
                                        BorderBrush="{x:Null}"
                                        Click="SmartPickPopupCloseButton_Click"
                                        Cursor="Hand"
                                        ToolTip="Close">
                                        <Viewbox Width="24" Height="24">
                                            <Canvas Width="24" Height="24">
                                                <Path
                                                    Stroke="#FF707079"
                                                    StrokeLineJoin="Round"
                                                    StrokeThickness="2">
                                                    <Path.Data>
                                                        <PathGeometry Figures="M6 18 18 6M6 6l12 12" />
                                                    </Path.Data>
                                                </Path>
                                            </Canvas>
                                        </Viewbox>
                                    </Button>
                                </Grid>

                                <!--  Separator after Header  -->
                                <Separator
                                    Grid.Row="0"
                                    Margin="0,25,0,0"
                                    Background="#FFD3D3D3" />

                                <!--  Content Grid with Separators and Spacing  -->
                                <Grid Grid.Row="1" Margin="0,11,0,0">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>


                                    <!--  Starting Point Section  -->
                                    <GroupBox
                                        x:Name="StartingPointSection"
                                        Grid.Row="0"
                                        Padding="0,5,0,0"
                                        FontFamily="Poppins"
                                        Foreground="#FF5E6694"
                                        Header="Write Starting Point or Pick it">
                                        <Grid Grid.Row="1" Margin="0,5,0,10">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" MinWidth="200" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <TextBox
                                                x:Name="SmartStartingPointTextBox"
                                                Grid.Column="0"
                                                Width="190"
                                                HorizontalAlignment="Left"
                                                AcceptsReturn="True"
                                                Foreground="#FF55657F"
                                                Style="{StaticResource RoundedTextBox}"
                                                Text=""
                                                TextWrapping="Wrap" />
                                            <Button
                                                x:Name="SmartPickStartingPointButton"
                                                Grid.Column="1"
                                                Width="60"
                                                Height="27"
                                                Background="#FFD9D9D9"
                                                BorderBrush="{x:Null}"
                                                BorderThickness="1"
                                                Click="SmartPickStartingPointButton_Click"
                                                Cursor="Hand"
                                                Foreground="#FF55657F"
                                                ToolTip="Draw listed points on the model">
                                                <Button.Template>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border
                                                            x:Name="Border"
                                                            Background="{TemplateBinding Background}"
                                                            BorderBrush="{TemplateBinding BorderBrush}"
                                                            BorderThickness="{TemplateBinding BorderThickness}"
                                                            CornerRadius="5">
                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter TargetName="Border" Property="Background" Value="#FFB5B5B5" />
                                                            </Trigger>
                                                            <Trigger Property="IsPressed" Value="True">
                                                                <Setter TargetName="Border" Property="Background" Value="#FF9B9B9B" />
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </Button.Template>
                                                <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                                    <Viewbox Width="20" Height="20">
                                                        <Canvas Width="25" Height="25">
                                                            <Path Fill="#FF687A99">
                                                                <Path.Data>
                                                                    <PathGeometry Figures="M12 14q-.825 0-1.412-.587T10 12t.588-1.412T12 10t1.413.588T14 12t-.587 1.413T12 14m-1-7V4q0-.425.288-.712T12 3t.713.288T13 4v3q0 .425-.288.713T12 8t-.712-.288T11 7m0 13v-3q0-.425.288-.712T12 16t.713.288T13 17v3q0 .425-.288.713T12 21t-.712-.288T11 20m6-9h3q.425 0 .713.288T21 12t-.288.713T20 13h-3q-.425 0-.712-.288T16 12t.288-.712T17 11M4 11h3q.425 0 .713.288T8 12t-.288.713T7 13H4q-.425 0-.712-.288T3 12t.288-.712T4 11" />
                                                                </Path.Data>
                                                            </Path>
                                                        </Canvas>
                                                    </Viewbox>
                                                    <TextBlock
                                                        Margin="2,0,0,0"
                                                        VerticalAlignment="Center"
                                                        FontFamily="{StaticResource PoppinsFont}"
                                                        FontSize="11"
                                                        Text="Pick"
                                                        ToolTip="Inserts points from the DataGrid as geometric entities in the model space." />
                                                </StackPanel>
                                            </Button>
                                        </Grid>
                                    </GroupBox>


                                    <!--  Selection Results Section (Initially Collapsed)  -->
                                    <GroupBox
                                        x:Name="SelectionResultSection"
                                        Grid.Row="1"
                                        Margin="0,3,0,0"
                                        Padding="4"
                                        FontFamily="Poppins"
                                        Foreground="#FF5E6694"
                                        Header="Selection Results"
                                        Visibility="Collapsed">
                                        <Grid
                                            Grid.Row="1"
                                            Width="265"
                                            Margin="0,3,0,0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <CheckBox
                                                x:Name="chkBlockSelection"
                                                Grid.Column="0"
                                                Height="15"
                                                VerticalContentAlignment="Center"
                                                Background="#FFCDD7E8"
                                                BorderBrush="{x:Null}"
                                                Content="12 Blocks"
                                                FontSize="12"
                                                Foreground="#FF687A99"
                                                Visibility="Collapsed" />
                                            <CheckBox
                                                x:Name="chkTextSelection"
                                                Grid.Column="1"
                                                Height="15"
                                                VerticalContentAlignment="Center"
                                                Background="#FFCDD7E8"
                                                BorderBrush="{x:Null}"
                                                Content="10 Text"
                                                FontSize="12"
                                                Foreground="#FF687A99"
                                                Visibility="Collapsed" />

                                            <CheckBox
                                                Grid.Column="2"
                                                Height="15"
                                                VerticalContentAlignment="Center"
                                                Background="#FFCDD7E8"
                                                BorderBrush="{x:Null}"
                                                Content="Combined"
                                                FontSize="12"
                                                Foreground="#FF687A99"
                                                Visibility="Collapsed" />

                                        </Grid>
                                    </GroupBox>


                                    <!--  Action for Selected Objects  -->
                                    <GroupBox
                                        Grid.Row="2"
                                        Margin="0,5,0,0"
                                        Padding="0,7,0,0"
                                        FontFamily="Poppins"
                                        Foreground="#FF5E6694"
                                        Header="Action for Selected Objects">
                                        <ComboBox
                                            x:Name="ObjectActionComboBox"
                                            Grid.Column="1"
                                            Width="115"
                                            VerticalAlignment="Center"
                                            Style="{StaticResource RoundedComboBox}">

                                            <ComboBoxItem
                                                Content="Delete"
                                                IsSelected="True"
                                                Tag="delete" />
                                            <ComboBoxItem Content="Move to Hidden Layer" Tag="move_hidden" />
                                            <ComboBoxItem Content="Keep" Tag="keep" />
                                        </ComboBox>
                                    </GroupBox>


                                    <!--  Insert Points Button  -->
                                    <Button
                                        x:Name="SmartInsertPointsButton"
                                        Grid.Row="3"
                                        Width="90"
                                        Height="30"
                                        Margin="0,5,0,0"
                                        Background="#FFD9D9D9"
                                        BorderBrush="{x:Null}"
                                        BorderThickness="1"
                                        Click="SmartInsertPointsButton_Click"
                                        Cursor="Hand"
                                        Foreground="#FF55657F"
                                        ToolTip="Draw listed points on the model">
                                        <Button.Template>
                                            <ControlTemplate TargetType="Button">
                                                <Border
                                                    x:Name="Border"
                                                    Background="{TemplateBinding Background}"
                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                    CornerRadius="5">
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter TargetName="Border" Property="Background" Value="#FFB5B5B5" />
                                                    </Trigger>
                                                    <Trigger Property="IsPressed" Value="True">
                                                        <Setter TargetName="Border" Property="Background" Value="#FF9B9B9B" />
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Button.Template>
                                        <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                            <Viewbox
                                                Width="19"
                                                Height="19"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Stretch="Uniform">
                                                <Canvas Width="22" Height="22">
                                                    <Canvas>
                                                        <Path
                                                            Stroke="#FF687A99"
                                                            StrokeEndLineCap="Round"
                                                            StrokeLineJoin="Round"
                                                            StrokeStartLineCap="Round">
                                                            <Path.Data>
                                                                <PathGeometry Figures="M18 21.187c.889-.202 1.564-.533 2.109-1.078C21.5 18.717 21.5 16.479 21.5 12c0-4.478 0-6.718-1.391-8.109S16.479 2.5 12 2.5c-4.478 0-6.718 0-8.109 1.391S2.5 7.521 2.5 12c0 4.478 0 6.718 1.391 8.109c.545.545 1.22.876 2.109 1.078" />
                                                            </Path.Data>
                                                        </Path>
                                                        <Path
                                                            Stroke="#FF687A99"
                                                            StrokeEndLineCap="Round"
                                                            StrokeLineJoin="Round"
                                                            StrokeStartLineCap="Round">
                                                            <Path.Data>
                                                                <PathGeometry Figures="M12 13.5v7m-3-2l3 3l3-3M2.5 9h19m-13-6.5V9m7-6.5V9" />
                                                            </Path.Data>
                                                        </Path>
                                                    </Canvas>
                                                </Canvas>
                                            </Viewbox>
                                            <TextBlock
                                                Margin="5,0,0,0"
                                                VerticalAlignment="Center"
                                                FontFamily="{StaticResource PoppinsFont}"
                                                FontSize="11"
                                                Text="List Points"
                                                ToolTip="Inserts points from the DataGrid as geometric entities in the model space." />
                                        </StackPanel>
                                    </Button>
                                </Grid>
                            </Grid>
                        </Border>
                    </Popup>

                </Grid>
            </GroupBox>

            <!--#endregion-->


            <!--#region Manual and Auto (Points) Picking-->
            <GroupBox
                Grid.Column="1"
                Width="217"
                Margin="1.5,0,5,0"
                Background="#80FFFFFF"
                BorderBrush="#80BFB6EA"
                BorderThickness="2"
                FontFamily="Poppins"
                FontWeight="DemiBold"
                Foreground="#FF55657F"
                Header="Points">
                <Grid Height="55">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <!--  Auto Pick  -->

                    <Grid Grid.Column="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <!--  Auto Pick Options Button  -->
                        <Button
                            x:Name="AutoPickOptionsButton"
                            Grid.Column="0"
                            Width="75"
                            Height="27"
                            Background="#FFD9D9D9"
                            BorderBrush="{x:Null}"
                            BorderThickness="1"
                            Click="AutoPickOptionsButton_Click"
                            Cursor="Hand"
                            Foreground="#FF55657F"
                            IsEnabled="True"
                            ToolTip="Draw listed points on the model"
                            Visibility="Collapsed">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border
                                        x:Name="Border"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter TargetName="Border" Property="Background" Value="#FFB5B5B5" />
                                            <Setter TargetName="Border" Property="BorderBrush" Value="#FFB5B5B5" />
                                        </Trigger>
                                        <Trigger Property="IsPressed" Value="True">
                                            <Setter TargetName="Border" Property="Background" Value="#FF9B9B9B" />
                                            <Setter TargetName="Border" Property="BorderBrush" Value="#FF9B9B9B" />
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Button.Template>

                            <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                <Viewbox
                                    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                                    Width="17"
                                    Height="17"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Stretch="Uniform">
                                    <Canvas Width="16" Height="16">

                                        <Path Fill="#FF687A99">
                                            <Path.Data>
                                                <PathGeometry Figures="M0 1.5A1.5 1.5 0 0 1 1.5 0h2A1.5 1.5 0 0 1 5 1.5v2A1.5 1.5 0 0 1 3.5 5h-2A1.5 1.5 0 0 1 0 3.5zM1.5 1a.5.5 0 0 0-.5.5v2a.5.5 0 0 0 .5.5h2a.5.5 0 0 0 .5-.5v-2a.5.5 0 0 0-.5-.5zM0 8a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2zm1 3v2a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2zm14-1V8a1 1 0 0 0-1-1H2a1 1 0 0 0-1 1v2zM2 8.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5m0 4a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5" />
                                            </Path.Data>
                                        </Path>
                                    </Canvas>
                                </Viewbox>
                                <TextBlock
                                    Margin="5,0,0,0"
                                    VerticalAlignment="Center"
                                    FontFamily="{StaticResource PoppinsFont}"
                                    FontSize="10"
                                    Text="Options"
                                    ToolTip="Inserts points from the DataGrid as geometric entities in the model space." />
                            </StackPanel>
                        </Button>

                        <!--  Auto Pick Instructions  -->
                        <TextBlock
                            x:Name="AutoPickGuidance"
                            Width="88"
                            HorizontalAlignment="Center"
                            FontSize="11.2"
                            FontWeight="SemiBold"
                            Foreground="#3B755F"
                            Text="Select points in model space and sort them by proximity."
                            TextAlignment="Left"
                            TextWrapping="Wrap"
                            Visibility="Visible" />

                        <!--  Auto Pick Button  -->
                        <Button
                            x:Name="AutoPickButton"
                            Grid.Column="1"
                            Width="50"
                            Height="50"
                            Margin="0,0,5,0"
                            Background="#FFD9D9D9"
                            BorderBrush="#FF55657F"
                            BorderThickness="0"
                            Click="AutoPickButton_Click"
                            Cursor="Hand"
                            FontFamily="{StaticResource PoppinsFont}"
                            Foreground="#FF55657F"
                            ToolTip="Automatically select multiple points in model space and order them based on proximity. Optionally, define a base point for reference.">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border
                                        x:Name="border"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        CornerRadius="10">
                                        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">

                                            <!--  Icon  -->

                                            <Viewbox
                                                xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                                                xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                                                Grid.Column="1"
                                                Width="27"
                                                Height="27"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Stretch="Uniform">
                                                <Canvas Width="24" Height="24">

                                                    <Path
                                                        Stroke="{TemplateBinding Foreground}"
                                                        StrokeEndLineCap="Round"
                                                        StrokeLineJoin="round"
                                                        StrokeStartLineCap="Round"
                                                        StrokeThickness="1.5">
                                                        <Path.Data>
                                                            <PathGeometry Figures="M4 8V6a2 2 0 0 1 2-2h2M4 16v2a2 2 0 0 0 2 2h2m8-16h2a2 2 0 0 1 2 2v2m-4 12h2a2 2 0 0 0 2-2v-2m-10-1v-4a2 2 0 1 1 4 0v4m-4-2h4" />
                                                        </Path.Data>
                                                    </Path>
                                                </Canvas>
                                            </Viewbox>


                                            <!--  Text  -->

                                            <TextBlock
                                                HorizontalAlignment="Center"
                                                FontSize="11"
                                                FontWeight="SemiBold"
                                                Foreground="{TemplateBinding Foreground}"
                                                Text="AUTO" />
                                        </StackPanel>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter TargetName="border" Property="Background" Value="#FFB5B5B5" />
                                            <Setter TargetName="border" Property="BorderBrush" Value="#FFB5B5B5" />
                                        </Trigger>
                                        <Trigger Property="IsPressed" Value="True">
                                            <Setter TargetName="border" Property="Background" Value="#FF9B9B9B" />
                                            <Setter TargetName="border" Property="BorderBrush" Value="#FF9B9B9B" />
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>

                        <!--  Auto Pick Popup  -->
                        <Popup
                            x:Name="AutoPickOptionsPopup"
                            AllowsTransparency="True"
                            IsOpen="False"
                            Placement="Bottom"
                            PlacementTarget="{Binding ElementName=AutoPickOptionsButton}"
                            PopupAnimation="Fade"
                            StaysOpen="False">
                            <Border
                                Width="300"
                                Height="Auto"
                                Padding="10"
                                Background="#FFFFFF"
                                BorderBrush="#80BFB6EA"
                                BorderThickness="2"
                                CornerRadius="15">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto" />
                                        <RowDefinition Height="Auto" />
                                    </Grid.RowDefinitions>


                                    <!--  Header with Title and Close Button  -->

                                    <Grid Grid.Row="0" Margin="0,0,0,10">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>

                                        <TextBlock
                                            Grid.Column="0"
                                            VerticalAlignment="Center"
                                            FontFamily="{StaticResource PoppinsFont}"
                                            FontSize="13"
                                            FontWeight="SemiBold"
                                            Foreground="#FF55657F"
                                            Text="Auto Pick Options" />

                                        <Button
                                            x:Name="AutoPickPopupCancelButton"
                                            Grid.Column="1"
                                            Width="30"
                                            Height="30"
                                            Background="Transparent"
                                            BorderBrush="{x:Null}"
                                            Click="AutoPickPopupCancelButton_Click"
                                            Cursor="Hand"
                                            ToolTip="Close">
                                            <Viewbox Width="24" Height="24">
                                                <Canvas Width="24" Height="24">
                                                    <Path
                                                        Stroke="#FF707079"
                                                        StrokeLineJoin="round"
                                                        StrokeThickness="2">
                                                        <Path.Data>
                                                            <PathGeometry Figures="M6 18 18 6M6 6l12 12" />
                                                        </Path.Data>
                                                    </Path>
                                                </Canvas>
                                            </Viewbox>
                                        </Button>
                                    </Grid>


                                    <!--  Separator  -->

                                    <Separator
                                        Grid.Row="0"
                                        Margin="0,25,0,0"
                                        Background="#FFD3D3D3" />


                                    <!--  Content  -->

                                    <GroupBox
                                        Grid.Row="1"
                                        Padding="0,6,0,0"
                                        FontFamily="Poppins"
                                        Foreground="#FF5E6694"
                                        Header="Enter or Select a Starting Point">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="AUto" />
                                            </Grid.ColumnDefinitions>

                                            <TextBox
                                                x:Name="StartingPointTextBox"
                                                Grid.Column="0"
                                                Width="Auto"
                                                VerticalAlignment="Center"
                                                HorizontalContentAlignment="Center"
                                                VerticalContentAlignment="Center"
                                                AcceptsReturn="True"
                                                Foreground="#FF55657F"
                                                Style="{StaticResource RoundedTextBox}"
                                                Text=""
                                                TextWrapping="Wrap" />

                                            <Button
                                                x:Name="PickStartingPointButton"
                                                Grid.Column="1"
                                                Width="60"
                                                Height="27"
                                                HorizontalAlignment="Center"
                                                VerticalAlignment="Center"
                                                Background="#FFD9D9D9"
                                                BorderBrush="{x:Null}"
                                                BorderThickness="1"
                                                Click="PickStartingPointButton_Click"
                                                Cursor="Hand"
                                                Foreground="#FF55657F"
                                                ToolTip="Draw listed points on the model">
                                                <Button.Template>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border
                                                            x:Name="Border"
                                                            Background="{TemplateBinding Background}"
                                                            BorderBrush="{TemplateBinding BorderBrush}"
                                                            BorderThickness="{TemplateBinding BorderThickness}"
                                                            CornerRadius="5">
                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter TargetName="Border" Property="Background" Value="#FFB5B5B5" />
                                                            </Trigger>
                                                            <Trigger Property="IsPressed" Value="True">
                                                                <Setter TargetName="Border" Property="Background" Value="#FF9B9B9B" />
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </Button.Template>

                                                <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                                    <Viewbox Width="20" Height="20">
                                                        <Canvas Width="22" Height="22">
                                                            <Path Fill="#FF687A99">
                                                                <Path.Data>
                                                                    <PathGeometry Figures="M12 14q-.825 0-1.412-.587T10 12t.588-1.412T12 10t1.413.588T14 12t-.587 1.413T12 14m-1-7V4q0-.425.288-.712T12 3t.713.288T13 4v3q0 .425-.288.713T12 8t-.712-.288T11 7m0 13v-3q0-.425.288-.712T12 16t.713.288T13 17v3q0 .425-.288.713T12 21t-.712-.288T11 20m6-9h3q.425 0 .713.288T21 12t-.288.713T20 13h-3q-.425 0-.712-.288T16 12t.288-.712T17 11M4 11h3q.425 0 .713.288T8 12t-.288.713T7 13H4q-.425 0-.712-.288T3 12t.288-.712T4 11" />
                                                                </Path.Data>
                                                            </Path>
                                                        </Canvas>
                                                    </Viewbox>
                                                    <TextBlock
                                                        Margin="5,0,0,0"
                                                        VerticalAlignment="Center"
                                                        FontFamily="{StaticResource PoppinsFont}"
                                                        FontSize="10"
                                                        Text="PICK"
                                                        ToolTip="Inserts points from the DataGrid as geometric entities in the model space." />
                                                </StackPanel>
                                            </Button>
                                        </Grid>
                                    </GroupBox>
                                </Grid>
                            </Border>
                        </Popup>
                    </Grid>

                    <!--  Manual Pick  -->
                    <ToggleButton
                        x:Name="ManualPickButton"
                        Grid.Column="1"
                        Width="50"
                        Height="50"
                        Margin="0,0,3,0"
                        VerticalAlignment="Center"
                        BorderThickness="0"
                        Checked="ManualPickButton_Checked"
                        Cursor="Hand"
                        FontFamily="{StaticResource PoppinsFont}"
                        ToolTip="Click in model space to manually select points. Each point will be numbered and added to the list."
                        Unchecked="ManualPickButton_Unchecked">
                        <ToggleButton.Template>
                            <ControlTemplate TargetType="ToggleButton">
                                <Border
                                    x:Name="border"
                                    Background="{TemplateBinding Background}"
                                    CornerRadius="10">
                                    <StackPanel
                                        Width="35"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center">

                                        <!--  Updated Icon Viewbox  -->

                                        <Viewbox
                                            Width="26"
                                            Height="26"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Stretch="Uniform">
                                            <Canvas Width="48" Height="48">
                                                <Ellipse
                                                    Canvas.Left="2.5"
                                                    Canvas.Top="2.5"
                                                    Width="43"
                                                    Height="43"
                                                    Stroke="{TemplateBinding Foreground}"
                                                    StrokeThickness="1" />
                                                <Ellipse
                                                    Canvas.Left="6.5"
                                                    Canvas.Top="6.5"
                                                    Width="35"
                                                    Height="35"
                                                    Stroke="{TemplateBinding Foreground}"
                                                    StrokeThickness="2" />
                                                <Ellipse
                                                    Canvas.Left="21.6"
                                                    Canvas.Top="21.6"
                                                    Width="4.5"
                                                    Height="4.5"
                                                    Fill="{TemplateBinding Foreground}" />
                                                <Path
                                                    Stroke="{TemplateBinding Foreground}"
                                                    StrokeEndLineCap="Round"
                                                    StrokeLineJoin="round"
                                                    StrokeStartLineCap="Round"
                                                    StrokeThickness="1">
                                                    <Path.Data>
                                                        <PathGeometry Figures="M2.5 24h14.183m0 1.591v-3.182M24 2.5v14.183m-1.591 0h3.182M45.5 24H31.317m0-1.591v3.182M24 45.5V31.317m1.591 0h-3.182" />
                                                    </Path.Data>
                                                </Path>
                                            </Canvas>
                                        </Viewbox>

                                        <!--  Updated Text  -->

                                        <TextBlock
                                            x:Name="buttonText"
                                            HorizontalAlignment="Center"
                                            FontFamily="{StaticResource PoppinsFont}"
                                            FontSize="11"
                                            FontWeight="Bold"
                                            Foreground="{TemplateBinding Foreground}"
                                            Text="PICK" />
                                    </StackPanel>
                                </Border>
                                <ControlTemplate.Triggers>

                                    <!--  Unchecked state  -->

                                    <Trigger Property="IsChecked" Value="False">
                                        <Setter Property="Background" Value="#FFD9D9D9" />
                                        <Setter Property="Foreground" Value="#FF55657F" />
                                    </Trigger>

                                    <!--  Checked state  -->

                                    <Trigger Property="IsChecked" Value="True">
                                        <Setter Property="Background" Value="#FF55657F" />
                                        <Setter Property="Foreground" Value="#FFD9D9D9" />
                                    </Trigger>

                                    <!--  Mouse-over effect for unchecked state  -->

                                    <MultiTrigger>
                                        <MultiTrigger.Conditions>
                                            <Condition Property="IsMouseOver" Value="True" />
                                            <Condition Property="IsChecked" Value="False" />
                                        </MultiTrigger.Conditions>
                                        <Setter TargetName="border" Property="Background" Value="#FFB5B5B5" />
                                    </MultiTrigger>

                                    <!--  Mouse-over effect for checked state  -->

                                    <MultiTrigger>
                                        <MultiTrigger.Conditions>
                                            <Condition Property="IsMouseOver" Value="True" />
                                            <Condition Property="IsChecked" Value="True" />
                                        </MultiTrigger.Conditions>
                                        <Setter TargetName="border" Property="Background" Value="#66758F" />
                                    </MultiTrigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </ToggleButton.Template>
                    </ToggleButton>
                </Grid>
            </GroupBox>

            <!--#endregion-->

        </Grid>
        <!--#endregion-->


        <!--#region Controls (Search, Delete All, Undo, and Redo)-->
        <Grid
            Grid.Row="2"
            Width="380"
            Margin="0,5,0,5"
            VerticalAlignment="Stretch">
            <Grid.ColumnDefinitions>
                <!--  Search Box Section  -->
                <ColumnDefinition Width="*" />
                <!--  Starting Number Box Container  -->
                <ColumnDefinition Width="auto" />
                <!--  Buttons Section  -->
                <ColumnDefinition Width="auto" />
            </Grid.ColumnDefinitions>

            <!--  Search Box Container  -->
            <Border
                Grid.Column="0"
                Height="30"
                Margin="0,0,10,0"
                Background="#F3F4F6"
                BorderBrush="#D1D5DB"
                BorderThickness="1"
                CornerRadius="8">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <!--  Search Icon  -->
                    <Border
                        Grid.Column="0"
                        Width="34"
                        Height="30"
                        VerticalAlignment="Center">
                        <Viewbox
                            Width="18"
                            Height="18"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Stretch="Uniform">
                            <Canvas Width="24" Height="24">
                                <Path
                                    Fill="#556085"
                                    Stroke="#6B7280"
                                    StrokeLineJoin="Round"
                                    StrokeThickness="1">
                                    <Path.Data>
                                        <PathGeometry Figures="M21.71 20.29L18 16.61A9 9 0 1 0 16.61 18l3.68 3.68a1 1 0 0 0 1.42 0a1 1 0 0 0 0-1.39M11 18a7 7 0 1 1 7-7a7 7 0 0 1-7 7" />
                                    </Path.Data>
                                </Path>
                            </Canvas>
                        </Viewbox>
                    </Border>

                    <!--  Search TextBox  -->
                    <TextBox
                        x:Name="SearchPointsTextBox"
                        Grid.Column="1"
                        Padding="0,0"
                        VerticalContentAlignment="Center"
                        Background="Transparent"
                        BorderThickness="0"
                        FontSize="13"
                        Foreground="#111827"
                        TextChanged="SearchPointsTextBox_TextChanged">
                        <TextBox.Resources>
                            <Style TargetType="{x:Type TextBox}">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="{x:Type TextBox}">
                                            <Border
                                                x:Name="border"
                                                Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}">
                                                <Grid>
                                                    <ScrollViewer
                                                        x:Name="PART_ContentHost"
                                                        Focusable="False"
                                                        HorizontalScrollBarVisibility="Hidden"
                                                        VerticalScrollBarVisibility="Hidden" />
                                                    <TextBlock
                                                        x:Name="WatermarkText"
                                                        VerticalAlignment="Center"
                                                        Foreground="#6B7280"
                                                        Text="Search Points..."
                                                        Visibility="Collapsed" />
                                                </Grid>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <MultiTrigger>
                                                    <MultiTrigger.Conditions>
                                                        <Condition Property="Text" Value="" />
                                                        <Condition Property="IsFocused" Value="False" />
                                                    </MultiTrigger.Conditions>
                                                    <Setter TargetName="WatermarkText" Property="Visibility" Value="Visible" />
                                                </MultiTrigger>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter TargetName="border" Property="BorderBrush" Value="#3B82F6" />
                                                </Trigger>
                                                <Trigger Property="IsFocused" Value="True">
                                                    <Setter TargetName="border" Property="BorderBrush" Value="#3B82F6" />
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </TextBox.Resources>
                    </TextBox>
                </Grid>
            </Border>


            <!--  Starting Number Box Container  -->
            <Border
                Grid.Column="1"
                Width="85"
                Height="30"
                Margin="0,0,10,0"
                Background="#F3F4F6"
                BorderBrush="#D1D5DB"
                BorderThickness="1"
                CornerRadius="8"
                ToolTip="Picking Starting Number">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <!--  Search Icon  -->
                    <Border
                        Grid.Column="0"
                        Width="34"
                        Height="30"
                        VerticalAlignment="Center">
                        <TextBlock
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            FontFamily="{StaticResource PoppinsFont}"
                            FontSize="18"
                            Foreground="#556085"
                            Text="#" />
                    </Border>

                    <!--  Starting Number TextBox  -->
                    <!--  TextChanged="StartingNumberTextBox_TextChanged"  -->
                    <TextBox
                        x:Name="StartingNumberTextBox"
                        Grid.Column="1"
                        Padding="0,0"
                        VerticalContentAlignment="Center"
                        Background="Transparent"
                        BorderThickness="0"
                        Foreground="#111827"
                        Text="1">
                        <TextBox.Resources>
                            <Style TargetType="{x:Type TextBox}">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="{x:Type TextBox}">
                                            <Border
                                                x:Name="border"
                                                Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}">
                                                <Grid>
                                                    <ScrollViewer
                                                        x:Name="PART_ContentHost"
                                                        Focusable="False"
                                                        HorizontalScrollBarVisibility="Hidden"
                                                        VerticalScrollBarVisibility="Hidden" />
                                                    <TextBlock
                                                        x:Name="WatermarkText"
                                                        VerticalAlignment="Center"
                                                        Foreground="#6B7280"
                                                        Text="PN..."
                                                        Visibility="Collapsed" />
                                                </Grid>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <MultiTrigger>
                                                    <MultiTrigger.Conditions>
                                                        <Condition Property="Text" Value="" />
                                                        <Condition Property="IsFocused" Value="False" />
                                                    </MultiTrigger.Conditions>
                                                    <Setter TargetName="WatermarkText" Property="Visibility" Value="Visible" />
                                                </MultiTrigger>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter TargetName="border" Property="BorderBrush" Value="#3B82F6" />
                                                </Trigger>
                                                <Trigger Property="IsFocused" Value="True">
                                                    <Setter TargetName="border" Property="BorderBrush" Value="#3B82F6" />
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </TextBox.Resources>
                    </TextBox>
                </Grid>
            </Border>

            <!--  Button Stack Panel  -->
            <StackPanel
                Grid.Column="2"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Orientation="Horizontal">
                <!--  Undo button (positioned on left)  -->
                <Button
                    x:Name="UndoBTN"
                    Width="26"
                    Height="26"
                    Background="#FFD9D9D9"
                    BorderThickness="0"
                    Click="UndoBTN_Click"
                    Cursor="Hand"
                    Foreground="#FF55657F"
                    ToolTip="Undo last action">
                    <Button.Template>
                        <ControlTemplate TargetType="{x:Type Button}">
                            <Border
                                x:Name="Border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="5 0 0 5">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="Border" Property="Background" Value="#FFB5B5B5" />
                                    <Setter TargetName="Border" Property="BorderBrush" Value="#FFB5B5B5" />
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter TargetName="Border" Property="Background" Value="#FF9B9B9B" />
                                    <Setter TargetName="Border" Property="BorderBrush" Value="#FF9B9B9B" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>

                    <Viewbox
                        Width="24"
                        Height="24"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Stretch="Uniform">
                        <Canvas Width="24" Height="24">
                            <Path Fill="#FF55657F">
                                <Path.Data>
                                    <PathGeometry Figures="M7.404 18v-1h7.254q1.556 0 2.65-1.067q1.096-1.067 1.096-2.606t-1.095-2.596q-1.096-1.058-2.651-1.058H6.916l2.965 2.965l-.708.708L5 9.173L9.173 5l.708.708l-2.965 2.965h7.742q1.963 0 3.355 1.354q1.39 1.354 1.39 3.3t-1.39 3.31T14.657 18z" />
                                </Path.Data>
                            </Path>
                        </Canvas>
                    </Viewbox>
                </Button>

                <!--  Redo button (positioned in middle)  -->
                <Button
                    x:Name="RedoBTN"
                    Width="26"
                    Height="26"
                    Margin="0,0,0,0"
                    Background="#FFD9D9D9"
                    BorderThickness="0"
                    Click="RedoBTN_Click"
                    Cursor="Hand"
                    Foreground="#FF55657F"
                    ToolTip="Redo last undone action">
                    <Button.Template>
                        <ControlTemplate TargetType="{x:Type Button}">
                            <Border
                                x:Name="Border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="0 5 5 0">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="Border" Property="Background" Value="#FFB5B5B5" />
                                    <Setter TargetName="Border" Property="BorderBrush" Value="#FFB5B5B5" />
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter TargetName="Border" Property="Background" Value="#FF9B9B9B" />
                                    <Setter TargetName="Border" Property="BorderBrush" Value="#FF9B9B9B" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>

                    <Viewbox
                        Width="24"
                        Height="24"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Stretch="Uniform">
                        <Canvas Width="24" Height="24">
                            <Path Fill="#FF55657F">
                                <Path.Data>
                                    <PathGeometry Figures="M9.342 18q-1.963 0-3.355-1.364t-1.39-3.309t1.39-3.3Q7.38 8.673 9.343 8.673h7.743L14.12 5.708L14.828 5L19 9.173l-4.173 4.173l-.708-.707l2.966-2.966H9.342q-1.556 0-2.65 1.058q-1.096 1.058-1.096 2.596t1.095 2.606Q7.787 17 9.342 17h7.254v1z" />
                                </Path.Data>
                            </Path>
                        </Canvas>
                    </Viewbox>
                </Button>

                <!--  Delete button (positioned on right)  -->
                <Button
                    x:Name="DeleteAllPointsBTN"
                    Width="26"
                    Height="26"
                    Margin="10,0,0,0"
                    Background="#FFD9D9D9"
                    BorderThickness="0"
                    Click="DeleteAllPointsBTN_Click"
                    Cursor="Hand"
                    Foreground="#FF55657F"
                    ToolTip="Delete all points">
                    <Button.Template>
                        <ControlTemplate TargetType="{x:Type Button}">
                            <Border
                                x:Name="Border"
                                Background="{TemplateBinding Background}"
                                CornerRadius="5">
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                            </Border>
                            <ControlTemplate.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter TargetName="Border" Property="Background" Value="#FFB5B5B5" />
                                    <Setter TargetName="Border" Property="BorderBrush" Value="#FFB5B5B5" />
                                </Trigger>
                                <Trigger Property="IsPressed" Value="True">
                                    <Setter TargetName="Border" Property="Background" Value="#FF9B9B9B" />
                                    <Setter TargetName="Border" Property="BorderBrush" Value="#FF9B9B9B" />
                                </Trigger>
                            </ControlTemplate.Triggers>
                        </ControlTemplate>
                    </Button.Template>
                    <Viewbox
                        Width="20"
                        Height="20"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Stretch="Uniform">
                        <Canvas Width="20" Height="20">
                            <Path Fill="#FF55657F">
                                <Path.Data>
                                    <PathGeometry Figures="M8.5 4h3a1.5 1.5 0 0 0-3 0m-1 0a2.5 2.5 0 0 1 5 0h5a.5.5 0 0 1 0 1h-1.054l-1.194 10.344A3 3 0 0 1 12.272 18H7.728a3 3 0 0 1-2.98-2.656L3.554 5H2.5a.5.5 0 0 1 0-1zM5.741 15.23A2 2 0 0 0 7.728 17h4.544a2 2 0 0 0 1.987-1.77L15.439 5H4.561zM8.5 7.5A.5.5 0 0 1 9 8v6a.5.5 0 0 1-1 0V8a.5.5 0 0 1 .5-.5M12 8a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0z" />
                                </Path.Data>
                            </Path>
                        </Canvas>
                    </Viewbox>
                </Button>
            </StackPanel>
        </Grid>

        <!--#endregion-->


        <!--#region DataGrid (Point List)-->
        <Border Grid.Row="3" Padding="3">
            <DataGrid
                x:Name="PointsDataGrid"
                Width="380"
                Height="350"
                MinHeight="200"
                HorizontalAlignment="Center"
                VerticalAlignment="Top"
                HorizontalContentAlignment="Center"
                VerticalContentAlignment="Center"
                AlternatingRowBackground="White"
                AlternationCount="2"
                AutoGenerateColumns="False"
                Background="#F5F5F5"
                BorderBrush="{x:Null}"
                CellStyle="{StaticResource PointsDataGridCellStyle}"
                ColumnHeaderHeight="30"
                FontFamily="{StaticResource PoppinsFont}"
                Foreground="#FF55657F"
                GridLinesVisibility="Vertical"
                HeadersVisibility="Column"
                HorizontalGridLinesBrush="{x:Null}"
                HorizontalScrollBarVisibility="Disabled"
                ItemsSource="{Binding Points}"
                MinColumnWidth="7"
                RowBackground="#FFE4E4E4"
                RowHeight="25"
                RowStyle="{StaticResource PointsDataGridRowStyle}"
                VerticalGridLinesBrush="White"
                VerticalScrollBarVisibility="Hidden">

                <!--  DataGrid Column Definitions *** TODO: sorting PointNumber  -->
                <DataGrid.Columns>
                    <DataGridTextColumn
                        Width="Auto"
                        MinWidth="20"
                        MaxWidth="45"
                        Binding="{Binding PointNumber}"
                        ElementStyle="{StaticResource DataGridTextCellStyle}"
                        Header="PN"
                        HeaderStyle="{StaticResource DataGridHeaderCellStyle}" />
                    <DataGridTextColumn
                        Width="*"
                        Binding="{Binding Easting}"
                        ElementStyle="{StaticResource DataGridTextCellStyle}"
                        Header="Easting"
                        HeaderStyle="{StaticResource DataGridHeaderCellStyle}" />
                    <DataGridTextColumn
                        Width="*"
                        Binding="{Binding Northing}"
                        ElementStyle="{StaticResource DataGridTextCellStyle}"
                        Header="Northing"
                        HeaderStyle="{StaticResource DataGridHeaderCellStyle}" />
                    <DataGridTextColumn
                        Width="*"
                        Binding="{Binding Elevation}"
                        ElementStyle="{StaticResource DataGridTextCellStyle}"
                        Header="Elevation"
                        HeaderStyle="{StaticResource DataGridHeaderCellStyle}" />
                    <DataGridTextColumn
                        Width="*"
                        Binding="{Binding Description}"
                        ElementStyle="{StaticResource DataGridTextCellStyle}"
                        Header="Description"
                        HeaderStyle="{StaticResource DataGridHeaderCellStyle}" />
                </DataGrid.Columns>

            </DataGrid>

        </Border>

        <!--#endregion-->


        <!--#region Footer (point counter, Draw Table, Formats Informations (Like SDR), and Export)-->
        <Grid
            Grid.Row="4"
            Width="380"
            Margin="0,5,0,5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="auto" />
                <ColumnDefinition Width="auto" />
            </Grid.ColumnDefinitions>

            <TextBlock
                x:Name="totalPointsCounter"
                Grid.Column="0"
                Width="126"
                Height="20"
                Margin="0,0,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Center"
                FontFamily="{StaticResource PoppinsFont}"
                Foreground="#FF55657F">

                <Run Text="Total Points: " />
                <Run
                    x:Name="totalPointsValue"
                    FontWeight="SemiBold"
                    Foreground="#FF4F4F8C"
                    Text="{Binding TotalPointsCount, Mode=OneWay, FallbackValue=0}" />
            </TextBlock>

            <!--  Add Table to Drawing Button  -->
            <Button
                x:Name="AddTableToDrawingButton"
                Grid.Column="2"
                Width="25"
                Height="25"
                Margin="0,0,7,0"
                Click="DrawPointsTableBTN_Click"
                Cursor="Hand"
                Style="{StaticResource IconButtonStyle}"
                ToolTip="Add table to drawing."
                Visibility="{Binding IsSettingsVisible, RelativeSource={RelativeSource AncestorType=UserControl}}">
                <Viewbox
                    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    Width="25"
                    Height="25"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Stretch="Uniform">
                    <Canvas Width="24" Height="24">

                        <Path Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}">
                            <Path.Data>
                                <PathGeometry Figures="M18 14h2v3h3v2h-3v3h-2v-3h-3v-2h3zM4 3h14a2 2 0 0 1 2 2v7.08a6 6 0 0 0-4.32.92H12v4h1.08c-.11.68-.11 1.35 0 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2m0 4v4h6V7zm8 0v4h6V7zm-8 6v4h6v-4z" />
                            </Path.Data>
                        </Path>
                    </Canvas>
                </Viewbox>
            </Button>

            <!--  Export Options Button  -->
            <Button
                x:Name="ExportOptionsButton"
                Grid.Column="4"
                Width="25"
                Height="25"
                Click="OpenExportDialogButton_Click"
                Cursor="Hand"
                Style="{StaticResource IconButtonStyle}"
                ToolTip="Export options and settings.">
                <Viewbox
                    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    Width="25"
                    Height="25"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Stretch="Uniform">
                    <Canvas Width="24" Height="24">

                        <Canvas>
                            <Path>
                                <Path.Data>
                                    <PathGeometry Figures="m12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035q-.016-.005-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.017-.018m.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093q.019.005.029-.008l.004-.014l-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01z" />
                                </Path.Data>
                            </Path>

                            <Path Fill="{Binding Foreground, RelativeSource={RelativeSource AncestorType=Button}}">
                                <Path.Data>
                                    <PathGeometry Figures="M13.586 2a2 2 0 0 1 1.284.467l.13.119L19.414 7a2 2 0 0 1 .578 1.238l.008.176V12h-2v-2h-4.5a1.5 1.5 0 0 1-1.493-1.356L12 8.5V4H6v16h6v2H6a2 2 0 0 1-1.995-1.85L4 20V4a2 2 0 0 1 1.85-1.995L6 2zm5.121 12.465l2.828 2.828a1 1 0 0 1 0 1.414l-2.828 2.828a1 1 0 1 1-1.414-1.414L18.414 19H14a1 1 0 1 1 0-2h4.414l-1.121-1.121a1 1 0 0 1 1.414-1.415ZM14 4.414V8h3.586z" />
                                </Path.Data>
                            </Path>
                        </Canvas>
                    </Canvas>
                </Viewbox>
            </Button>

            <!--  Export Dialog Popup  -->
            <Popup
                x:Name="ExportDialogPopup"
                AllowsTransparency="True"
                IsOpen="False"
                Placement="Top"
                PlacementTarget="{Binding ElementName=ExportOptionsButton}"
                PopupAnimation="Fade"
                StaysOpen="False">

                <Border
                    Width="285"
                    Height="Auto"
                    Padding="10"
                    Background="#FFFFFF"
                    BorderBrush="#80BFB6EA"
                    BorderThickness="2"
                    CornerRadius="15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <!--  Header  -->
                            <RowDefinition Height="Auto" />
                            <!--  Content  -->
                            <RowDefinition Height="Auto" />
                            <!--  footer export button  -->
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <!--  Header with Title and Close Button  -->
                        <Grid Grid.Row="0" Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <!--  Title  -->
                            <TextBlock
                                Grid.Column="0"
                                VerticalAlignment="Center"
                                FontFamily="{StaticResource PoppinsFont}"
                                FontSize="13"
                                FontWeight="SemiBold"
                                Foreground="#FF55657F"
                                Text="Export Points" />

                            <!--  Close Button  -->
                            <Button
                                x:Name="CloseExportPopupButton"
                                Grid.Column="1"
                                Width="30"
                                Height="30"
                                Background="Transparent"
                                BorderBrush="{x:Null}"
                                Click="CloseExportPopupButton_Click"
                                Cursor="Hand"
                                ToolTip="Close">
                                <Viewbox
                                    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                                    Width="24"
                                    Height="24"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Stretch="Uniform">
                                    <Canvas Width="24" Height="24">
                                        <Path
                                            Stroke="#FF707079"
                                            StrokeLineJoin="round"
                                            StrokeThickness="2">
                                            <Path.Data>
                                                <PathGeometry Figures="M6 18 18 6M6 6l12 12" />
                                            </Path.Data>
                                        </Path>
                                    </Canvas>
                                </Viewbox>
                            </Button>

                            <!--  Separator below  -->
                            <Separator
                                Grid.Row="1"
                                Grid.ColumnSpan="2"
                                Margin="0,5,0,0"
                                Background="#FFD3D3D3" />
                        </Grid>

                        <!--  Content  -->
                        <Grid Grid.Row="1">
                            <Grid.RowDefinitions>
                                <!--  Select File Format  -->
                                <RowDefinition Height="Auto" />
                                <!--  Export Settings  -->
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>

                            <GroupBox
                                Grid.Row="0"
                                Padding="5,10,10,5"
                                FontFamily="{StaticResource PoppinsFont}"
                                Foreground="#FF31516D"
                                Header="Select Export format">
                                <ComboBox
                                    x:Name="ExportFileFormatComboBox"
                                    Width="205"
                                    Height="27"
                                    Margin="0,0,0,0"
                                    FontFamily="{StaticResource PoppinsFont}"
                                    SelectionChanged="ExportFileFormatComboBox_SelectionChanged"
                                    Style="{StaticResource RoundedComboBox}">

                                    <ComboBoxItem
                                        Foreground="Bisque"
                                        IsEnabled="False"
                                        IsSelected="True">
                                        Select points format...
                                    </ComboBoxItem>
                                    <ComboBoxItem Content="CSV (Comma delimited) (*.csv)" Tag=".csv" />
                                    <ComboBoxItem Content="Text (Tab delimited) (*.txt)" Tag=".txt" />
                                    <ComboBoxItem Content="KML (*.kml)" Tag=".kml" />
                                    <ComboBoxItem Content="SDR33 (Sokkia) (*.sdr)" Tag=".sdr" />
                                    <ComboBoxItem Content="IDX (Leica) (*.idx)" Tag=".idx" />
                                    <ComboBoxItem Content="GSI (Leica) (*.gsi)" Tag=".gsi" />
                                </ComboBox>
                            </GroupBox>

                            <GroupBox
                                Grid.Row="1"
                                MinHeight="30"
                                Margin="0,5,0,0"
                                Padding="5,10,10,5"
                                FontFamily="{StaticResource PoppinsFont}"
                                Foreground="#FF31516D"
                                Header="Settings">
                                <Grid>
                                    <!--  Empty Settings Panel  -->
                                    <Grid
                                        x:Name="ExEmptySettings"
                                        MinHeight="40"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Top"
                                        ToolTip="No file selected"
                                        Visibility="Visible">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>

                                        <ContentControl
                                            Grid.Column="0"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Top"
                                            Content="{StaticResource EmptySettingsIcon}" />
                                    </Grid>

                                    <!--  CSV/TXT Settings Panel  -->
                                    <Grid x:Name="ExCSVSettings" Visibility="Collapsed">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>

                                        <CheckBox
                                            x:Name="PointsHaveNumberCheckBox"
                                            Grid.Column="0"
                                            Margin="0,0,10,0"
                                            Padding="4,0,4,0"
                                            HorizontalContentAlignment="Center"
                                            VerticalContentAlignment="Center"
                                            Background="#FFCDD7E8"
                                            BorderBrush="{x:Null}"
                                            Content="PN"
                                            FontFamily="{StaticResource PoppinsFont}"
                                            Foreground="#FF687A99"
                                            IsChecked="True" />

                                        <CheckBox
                                            x:Name="XYRadioButton"
                                            Grid.Column="1"
                                            Margin="0,0,10,0"
                                            Padding="4,0,0,0"
                                            VerticalContentAlignment="Center"
                                            Background="#FFCDD7E8"
                                            BorderBrush="{x:Null}"
                                            Content="X, Y Order"
                                            FontFamily="{StaticResource PoppinsFont}"
                                            Foreground="#FF687A99"
                                            IsChecked="True" />

                                        <CheckBox
                                            x:Name="FileHasHeadersCheckBox"
                                            Grid.Column="2"
                                            VerticalContentAlignment="Center"
                                            Background="#FFCDD7E8"
                                            BorderBrush="{x:Null}"
                                            Content="Has Headers"
                                            FontFamily="{StaticResource PoppinsFont}"
                                            Foreground="#FF687A99"
                                            IsChecked="False" />
                                    </Grid>

                                    <!--  GSI Settings Panel  -->
                                    <Grid
                                        x:Name="ExGSISettings"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Visibility="Collapsed">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <ColumnDefinition Width="Auto" />
                                        </Grid.ColumnDefinitions>

                                        <TextBlock
                                            Grid.Column="0"
                                            Margin="0,0,10,0"
                                            VerticalAlignment="Center"
                                            FontFamily="{StaticResource PoppinsFont}"
                                            FontSize="13.5"
                                            Foreground="#FF687A99"
                                            Text="GSI variant" />

                                        <ComboBox
                                            x:Name="GSIFormatCB"
                                            Grid.Column="1"
                                            Width="75"
                                            Height="30"
                                            VerticalAlignment="Center"
                                            FontFamily="{StaticResource PoppinsFont}"
                                            FontSize="13"
                                            Style="{StaticResource RoundedComboBox}">
                                            <ComboBoxItem Content="GSI 8" />
                                            <ComboBoxItem Content="GSI 16" IsSelected="True" />
                                        </ComboBox>
                                    </Grid>

                                    <!--  KML Settings Panel  -->
                                    <Grid
                                        x:Name="ExKMLSettings"
                                        MaxWidth="250"
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Visibility="Collapsed">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto" />
                                            <!--  Hemisphere label  -->
                                            <ColumnDefinition Width="Auto" />
                                            <!--  Hemisphere ComboBox  -->
                                            <ColumnDefinition Width="Auto" />
                                            <!--  Zone label  -->
                                            <ColumnDefinition Width="Auto" />
                                            <!--  Zone ComboBox  -->
                                        </Grid.ColumnDefinitions>

                                        <!--  Hemisphere Label  -->
                                        <TextBlock
                                            Grid.Column="0"
                                            Margin="0,0,5,0"
                                            VerticalAlignment="Center"
                                            FontFamily="{StaticResource PoppinsFont}"
                                            FontSize="12"
                                            Foreground="#FF687A99"
                                            Text="Hemisphere:" />

                                        <!--  Hemisphere ComboBox  -->
                                        <ComboBox
                                            x:Name="HemisphereCB"
                                            Grid.Column="1"
                                            Width="Auto"
                                            MinWidth="60"
                                            Margin="0,0,10,0"
                                            VerticalContentAlignment="Center"
                                            FontFamily="{StaticResource PoppinsFont}"
                                            Style="{StaticResource ResourceKey=RoundedComboBox}">
                                            <ComboBoxItem Content="North" IsSelected="True" />
                                            <ComboBoxItem Content="South" />
                                        </ComboBox>

                                        <!--  Zone Label  -->
                                        <TextBlock
                                            Grid.Column="2"
                                            Margin="0,0,5,0"
                                            VerticalAlignment="Center"
                                            FontFamily="{StaticResource PoppinsFont}"
                                            FontSize="12"
                                            Foreground="#FF687A99"
                                            Text="Zone:" />

                                        <!--  Zone ComboBox  -->
                                        <ComboBox
                                            x:Name="ZoneCB"
                                            Grid.Column="3"
                                            Width="Auto"
                                            MinWidth="39"
                                            MinHeight="25"
                                            HorizontalContentAlignment="Center"
                                            VerticalContentAlignment="Center"
                                            FontFamily="{StaticResource PoppinsFont}"
                                            SelectedIndex="36"
                                            Style="{StaticResource ResourceKey=RoundedComboBox}">
                                            <!--  UTM Zone numbers 1-60  -->
                                            <ComboBoxItem Content="1" />
                                            <ComboBoxItem Content="2" />
                                            <ComboBoxItem Content="3" />
                                            <ComboBoxItem Content="4" />
                                            <ComboBoxItem Content="5" />
                                            <ComboBoxItem Content="6" />
                                            <ComboBoxItem Content="7" />
                                            <ComboBoxItem Content="8" />
                                            <ComboBoxItem Content="9" />
                                            <ComboBoxItem Content="10" />
                                            <ComboBoxItem Content="11" />
                                            <ComboBoxItem Content="12" />
                                            <ComboBoxItem Content="13" />
                                            <ComboBoxItem Content="14" />
                                            <ComboBoxItem Content="15" />
                                            <ComboBoxItem Content="16" />
                                            <ComboBoxItem Content="17" />
                                            <ComboBoxItem Content="18" />
                                            <ComboBoxItem Content="19" />
                                            <ComboBoxItem Content="20" />
                                            <ComboBoxItem Content="21" />
                                            <ComboBoxItem Content="22" />
                                            <ComboBoxItem Content="23" />
                                            <ComboBoxItem Content="24" />
                                            <ComboBoxItem Content="25" />
                                            <ComboBoxItem Content="26" />
                                            <ComboBoxItem Content="27" />
                                            <ComboBoxItem Content="28" />
                                            <ComboBoxItem Content="29" />
                                            <ComboBoxItem Content="30" />
                                            <ComboBoxItem Content="31" />
                                            <ComboBoxItem Content="32" />
                                            <ComboBoxItem Content="33" />
                                            <ComboBoxItem Content="34" />
                                            <ComboBoxItem Content="35" />
                                            <ComboBoxItem Content="36" />
                                            <ComboBoxItem Content="37" IsSelected="True" />
                                            <ComboBoxItem Content="38" />
                                            <ComboBoxItem Content="39" />
                                            <ComboBoxItem Content="40" />
                                            <ComboBoxItem Content="41" />
                                            <ComboBoxItem Content="42" />
                                            <ComboBoxItem Content="43" />
                                            <ComboBoxItem Content="44" />
                                            <ComboBoxItem Content="45" />
                                            <ComboBoxItem Content="46" />
                                            <ComboBoxItem Content="47" />
                                            <ComboBoxItem Content="48" />
                                            <ComboBoxItem Content="49" />
                                            <ComboBoxItem Content="50" />
                                            <ComboBoxItem Content="51" />
                                            <ComboBoxItem Content="52" />
                                            <ComboBoxItem Content="53" />
                                            <ComboBoxItem Content="54" />
                                            <ComboBoxItem Content="55" />
                                            <ComboBoxItem Content="56" />
                                            <ComboBoxItem Content="57" />
                                            <ComboBoxItem Content="58" />
                                            <ComboBoxItem Content="59" />
                                            <ComboBoxItem Content="60" />
                                        </ComboBox>
                                    </Grid>

                                </Grid>
                            </GroupBox>

                        </Grid>

                        <!--  Footer with Export Button  -->
                        <Button
                            x:Name="ExportButton"
                            Grid.Row="2"
                            Width="85"
                            Height="30"
                            Margin="0,5,0,0"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Background="#FFD9D9D9"
                            BorderBrush="{x:Null}"
                            Click="ExportButton_Click"
                            Cursor="Hand"
                            Foreground="#FF55657F"
                            ToolTip="Export points list according to selected format and settings">
                            <Button.Template>
                                <ControlTemplate TargetType="{x:Type Button}">
                                    <Border
                                        x:Name="Border"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        CornerRadius="5">
                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" />
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter TargetName="Border" Property="Background" Value="#FFB5B5B5" />
                                        </Trigger>
                                        <Trigger Property="IsPressed" Value="True">
                                            <Setter TargetName="Border" Property="Background" Value="#FF9B9B9B" />
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Button.Template>
                            <StackPanel
                                Margin="-3,0,0,0"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Center"
                                Orientation="Horizontal">
                                <Viewbox
                                    Width="18"
                                    Height="18"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Top"
                                    Stretch="Uniform">
                                    <Canvas Width="16" Height="16">
                                        <Path Data="M9.85903,5.01999C9.90547,5.06912 9.96147,5.10852 10.0237,5.13585 10.0859,5.16317 10.1531,5.17787 10.2212,5.17905 10.2893,5.18024 10.3569,5.16789 10.4201,5.14274 10.4833,5.11759 10.5406,5.08016 10.5888,5.03268 10.637,4.98521 10.675,4.92865 10.7005,4.86639 10.726,4.80413 10.7385,4.73745 10.7373,4.67031 10.7361,4.60318 10.7212,4.53697 10.6935,4.47563 10.6657,4.4143 10.6258,4.3591 10.5759,4.31333L8.54695,2.31333C8.45184,2.21969 8.32292,2.1671 8.1885,2.1671 8.05408,2.1671 7.92515,2.21969 7.83004,2.31333L5.80106,4.31333C5.71146,4.40811 5.66268,4.53347 5.665,4.66301 5.66731,4.79254 5.72055,4.91613 5.81349,5.00774 5.90642,5.09935 6.0318,5.15182 6.16322,5.15411 6.29463,5.15639 6.42181,5.10831 6.51796,5.01999L7.68125,3.87333 7.68125,9.33333C7.68125,9.46593 7.73469,9.59311 7.82982,9.68688 7.92495,9.78065 8.05397,9.83333 8.1885,9.83333 8.32303,9.83333 8.45205,9.78065 8.54717,9.68688 8.6423,9.59311 8.69574,9.46593 8.69574,9.33333L8.69574,3.87333 9.85903,5.01999z" Fill="#FF687A99" />
                                        <Path Data="M14.1064 8C14.1064 7.86739 14.0529 7.74022 13.9578 7.64645C13.8627 7.55268 13.7337 7.5 13.5991 7.5C13.4646 7.5 13.3356 7.55268 13.2405 7.64645C13.1453 7.74022 13.0919 7.86739 13.0919 8C13.0919 8.63472 12.9651 9.26323 12.7186 9.84964C12.4722 10.436 12.111 10.9689 11.6557 11.4177C11.2004 11.8665 10.6599 12.2225 10.0649 12.4654C9.47004 12.7083 8.83243 12.8333 8.1885 12.8333C7.54458 12.8333 6.90697 12.7083 6.31206 12.4654C5.71716 12.2225 5.17661 11.8665 4.72129 11.4177C4.26597 10.9689 3.90479 10.436 3.65837 9.84964C3.41195 9.26323 3.28512 8.63472 3.28512 8C3.28512 7.86739 3.23168 7.74022 3.13655 7.64645C3.04143 7.55268 2.91241 7.5 2.77788 7.5C2.64335 7.5 2.51433 7.55268 2.4192 7.64645C2.32407 7.74022 2.27063 7.86739 2.27063 8C2.27063 9.5471 2.89412 11.0308 4.00394 12.1248C5.11375 13.2188 6.61899 13.8333 8.1885 13.8333C9.75802 13.8333 11.2633 13.2188 12.3731 12.1248C13.4829 11.0308 14.1064 9.5471 14.1064 8Z" Fill="#FF687A99" />
                                    </Canvas>
                                </Viewbox>
                                <TextBlock
                                    Margin="2,0,0,0"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    FontFamily="{StaticResource PoppinsFont}"
                                    FontSize="11"
                                    Text="Export" />
                            </StackPanel>
                        </Button>
                    </Grid>
                </Border>

            </Popup>

        </Grid>
        <!--#endregion-->


    </Grid>
</UserControl>
