﻿using Autodesk.AutoCAD.ApplicationServices;
using Autodesk.AutoCAD.DatabaseServices;
using System;

namespace SPM_NET47_2019_2020.Helpers
{
    public static class AutoCADHelper
    {
        public static void ExecuteInTransaction(Action<Transaction> action)
        {
            Document doc = Application.DocumentManager.MdiActiveDocument;
            Database db = doc.Database;
            using (Transaction trans = db.TransactionManager.StartTransaction())
            {
                try
                {
                    action(trans);
                    trans.Commit();
                }
                catch
                {
                    trans.Abort();
                    throw;
                }
            }
        }
    }
}
